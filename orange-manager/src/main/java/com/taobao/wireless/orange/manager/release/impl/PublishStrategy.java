package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.ConditionManager;
import com.taobao.wireless.orange.manager.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.manager.NamespaceVersionManager;
import com.taobao.wireless.orange.manager.model.Condition;
import com.taobao.wireless.orange.manager.model.NamespaceIdNamePairBO;
import com.taobao.wireless.orange.manager.model.NamespaceVersionContentBO;
import com.taobao.wireless.orange.manager.release.AbstractReleaseOperationTemplate;
import com.taobao.wireless.orange.manager.release.ReleaseOperationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_RELEASE;

/**
 * 发布操作策略实现
 */
@Component
public class PublishOperationStrategy extends AbstractReleaseOperationTemplate {

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private ConditionManager conditionManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.RELEASE;
    }

    @Override
    public void validateStatus(ReleaseOperationContext context) {
        // 可以根据需要添加状态验证逻辑
        // if (!ReleaseOrderStatus.VERIFY_PASS.equals(context.getReleaseOrder().getStatus())) {
        //     throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_NOT_VERIFY_PASS);
        // }
    }

    @Override
    public void validatePermission(ReleaseOperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getOwners().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    @Override
    public void validateParameters(ReleaseOperationContext context) {
        // TODO: 实现具体的入参校验逻辑
        // 发布操作一般不需要额外参数，基础校验已在context中完成
    }

    @Override
    public void executeOperation(ReleaseOperationContext context) {
        String releaseVersion = context.getReleaseVersion();
        var releaseOrder = context.getReleaseOrder();

        // 上线参数版本
        onlineParameterVersion(releaseVersion);

        // 上线条件版本
        onlineConditionVersion(releaseVersion);

        // 上线参数条件版本
        onlineParameterConditionVersion(releaseVersion);

        // namespace 版本号递增
        var namespaceVersion = namespaceVersionManager.upgradeVersion(
                releaseOrder.getNamespaceId(), releaseVersion, FINISH_RELEASE);

        // 将正式版本内容保存（用于回滚）
        saveNamespaceVersionContent(releaseOrder, namespaceVersion);
    }

    @Override
    public ReleaseOrderStatus getTargetStatus() {
        return ReleaseOrderStatus.RELEASED;
    }

    private void onlineParameterVersion(String releaseVersion) {
        List<OParameterVersionDO> parameters = parameterVersionDAO
                .lambdaQuery()
                .select(OParameterVersionDO::getParameterId, OParameterVersionDO::getChangeType)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameters)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        parameterVersionDAO.lambdaUpdate()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OParameterVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的参数对象状态置为删除
        List<String> deleteParameterIds = parameters.stream()
                .filter(p -> ChangeType.DELETE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, deleteParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.INVALID)
                    .update();
        }

        // 将新增的参数对象状态置从初始改为已发布
        List<String> newParameterIds = parameters.stream()
                .filter(p -> ChangeType.CREATE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, newParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.ONLINE)
                    .update();
        }
    }

    private void onlineConditionVersion(String releaseVersion) {
        List<OConditionVersionDO> conditions = conditionVersionDAO
                .lambdaQuery()
                .select(OConditionVersionDO::getConditionId, OConditionVersionDO::getChangeType)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        // 将历史发布版本标记为过期
        List<String> conditionIds = conditions.stream()
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        conditionVersionDAO.lambdaUpdate()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的条件对象状态置为删除
        List<String> deleteConditionIds = conditions.stream()
                .filter(c -> ChangeType.DELETE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, deleteConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.INVALID)
                    .update();
        }

        // 将新增的条件对象状态置为已发布
        List<String> newConditionIds = conditions.stream()
                .filter(c -> ChangeType.CREATE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, newConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.ONLINE)
                    .update();
        }
    }

    private void onlineParameterConditionVersion(String releaseVersion) {
        var parameterConditionVersions = parameterConditionVersionDAO.lambdaQuery()
                .select(OParameterConditionVersionDO::getParameterId, OParameterConditionVersionDO::getConditionId)
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }

        // 将历史发布版本标记为过期
        var updateChainWrapper = parameterConditionVersionDAO.lambdaUpdate();
        for (var parameterCondition : parameterConditionVersions) {
            updateChainWrapper.or(i -> i
                    .eq(OParameterConditionVersionDO::getParameterId, parameterCondition.getParameterId())
                    .eq(OParameterConditionVersionDO::getConditionId, parameterCondition.getConditionId())
                    .eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED));
        }
        updateChainWrapper.set(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED).update();

        // 将本次发布版本标记为发布
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();
    }

    /**
     * 将正式内容保存（用于回滚）
     *
     * @param releaseOrder
     * @param namespaceVersion
     */
    private void saveNamespaceVersionContent(OReleaseOrderDO releaseOrder, String namespaceVersion) {
        ONamespaceVersionContentDO namespaceVersionContentDO = new ONamespaceVersionContentDO();
        namespaceVersionContentDO.setNamespaceId(releaseOrder.getNamespaceId());
        namespaceVersionContentDO.setNamespaceVersion(namespaceVersion);
        namespaceVersionContentDO.setAppKey(releaseOrder.getAppKey());

        NamespaceIdNamePairBO namespace = Optional.ofNullable(namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId()))
                .map(n -> NamespaceIdNamePairBO.builder()
                        .name(n.getName())
                        .namespaceId(n.getNamespaceId())
                        .build())
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
        Optional.ofNullable(fullReleaseConfigGenerator.generate(namespace, "0"))
                .ifPresent(content -> {
                    var namespaceVersionContent = new NamespaceVersionContentBO();
                    namespaceVersionContent.setParameters(content.getParameters());
                    if (CollectionUtils.isNotEmpty(content.getConditions())) {
                        List<String> conditionIds = content.getConditions()
                                .stream()
                                .map(Condition::getId)
                                .collect(Collectors.toList());
                        var conditionId2Version = conditionManager.getOnlineConditionVersions(conditionIds);
                        var conditionId2Name = conditionDAO.lambdaQuery()
                                .select(OConditionDO::getConditionId, OConditionDO::getName)
                                .in(OConditionDO::getConditionId, conditionIds)
                                .list()
                                .stream()
                                .collect(Collectors.toMap(OConditionDO::getConditionId, OConditionDO::getName));
                        var conditions = content.getConditions().stream().map(c -> {
                            var condition = new NamespaceVersionContentBO.Condition();
                            condition.setConditionId(c.getId());
                            condition.setName(conditionId2Name.get(c.getId()));
                            condition.setExpression(c.getExpression());
                            condition.setReleaseVersion(conditionId2Version.get(c.getId()).getReleaseVersion());
                            return condition;
                        }).toList();
                        namespaceVersionContent.setConditions(conditions);
                    }
                    namespaceVersionContentDO.setContent(JSON.toJSONString(namespaceVersionContent));
                });
        namespaceVersionContentDAO.save(namespaceVersionContentDO);
    }
}